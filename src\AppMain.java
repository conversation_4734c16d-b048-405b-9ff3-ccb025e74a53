import controller.Controller;
import model.Model;
import view.View;

public class AppMain
{
    public static void main(String[] args)
    {


        for(int i = 0; i < 30; i++)System.out.println("david the king");
        

        // Model model = new Model();
        // View view = new View();
        // Controller controller = new Controller(view, model);
        // view.setController(controller);
        // controller.runGame();
    }
}

package util;

import controller.Constants;
import javax.sound.sampled.Clip;
import javax.sound.sampled.FloatControl;

public class Sound implements Constants {
    /**
     * Play a sound once
     * @param clip The clip to play
     */
    public static void play(Clip clip) {
        //Clip clip = getSoundClip(soundType);
        if(!SOUND) return;
        if (clip != null) {
            clip.setFramePosition(0);
            clip.start();
        }
    }
    
    /**
     * Loop a sound continuously at a given volume and frame position
     * @param clip The clip to loop
     */
    public static void loop(Clip clip, float gainPar, int frame) {
        if(!SOUND) return;
        //Clip clip = getSoundClip(soundType);
        if (clip != null) {
            // Set volume to gainPar% of max volume
            try {
                FloatControl gainControl = (FloatControl) clip.getControl(FloatControl.Type.MASTER_GAIN);
                float range = gainControl.getMaximum() - gainControl.getMinimum();
                float gain = (range * gainPar) + gainControl.getMinimum();
                gainControl.setValue(gain);
            } catch (Exception e) {
                Debug.logE("Could not set volume: " + e.getMessage());
            }
            
            clip.setFramePosition(frame);
            clip.loop(Clip.LOOP_CONTINUOUSLY);
        }
    }
    /**
     * Stop a sound and remember its position
     * @param clip The clip to stop
     */
    public static void pause(Clip clip) {
        if (clip != null && clip.isRunning()) {
            clip.stop();
        }
    }

    /**
     * Continue playing a sound from where it was stopped
     * @param clip The clip to continue
     */
    public static void resume(Clip clip) {
        if (clip != null && !clip.isRunning()) {
            // Check if the clip was previously set to loop
            if (clip.getFramePosition() > 0) {
                clip.loop(Clip.LOOP_CONTINUOUSLY);
            } else {
                clip.start();
            }
        }
    }
    
    
    public static void loop(Clip soundType) {
        loop(soundType, 0.7f, 0);
    }
    
    /**
     * Stop a currently playing sound
     * @param clip The clip to stop
     */
    public static void stop(Clip clip) {
        if (clip != null) {
            clip.stop();
        }
    }

    /**
     * Stop all sounds
     */
    public static void stopAll() {
        if (SOUND_TAP != null) SOUND_TAP.stop();
        if (SOUND_START != null) SOUND_START.stop();
        if (SOUND_BACKGROUND != null) SOUND_BACKGROUND.stop();
        if (SOUND_AI != null) SOUND_AI.stop();
        if (SOUND_WINNER != null) SOUND_WINNER.stop();   
        if (SOUND_LOSER != null) SOUND_LOSER.stop();           
    }
}

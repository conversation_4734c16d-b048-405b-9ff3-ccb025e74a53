package util;

/**
 * Utility for measuring execution time of code blocks
 */
public class Timer {
    private long startTime;
    private long endTime;
    private boolean running;
    private String label;
    
    /**
     * Create a new timer with an optional label
     */
    public Timer(String label) {
        this.label = label;
        this.running = false;
    }
    
    public Timer() {
        this("Timer");
    }
    
    /**
     * Start the timer
     */
    public void start() {
        startTime = System.nanoTime();
        running = true;
    }
    
    /**
     * Stop the timer
     */
    public void stop() {
        if (!running) {
            return;
        }
        
        endTime = System.nanoTime();
        running = false;
    }
    
    /**
     * Get elapsed time in milliseconds
     */
    public long getMillisPassed() {
        if (running) {
            return (System.nanoTime() - startTime) / 1_000_000;
        } else {
            return (endTime - startTime) / 1_000_000;
        }
    }
    
    /**
     * Print the elapsed time with the label
     */
    public void printElapsed() {
        long elapsed = getMillisPassed();
        Debug.logI(label + ": " + elapsed + "ms");
    }
    
    /**
     * Static utility to time a code block
     */
    public static long time(Runnable codeBlock) {
        Timer timer = new Timer();
        timer.start();
        codeBlock.run();
        timer.stop();
        return timer.getMillisPassed();
    }
}
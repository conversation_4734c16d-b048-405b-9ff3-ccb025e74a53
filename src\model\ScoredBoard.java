package model;

public class ScoredBoard extends Board {
    private int score;
    
    public ScoredBoard(Board board, int score) {
        super(board); // Use the copy constructor from Board
        this.score = score;
    }
    
    public int getScore() {
        return score;
    }
    
    public void setScore(int score) {
        this.score = score;
    }
    
    @Override
    public String toString() {
        return "ScoredBoard [score=" + score + "]";
    }
}

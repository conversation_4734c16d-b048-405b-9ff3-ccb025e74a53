package model;

import controller.Constants;
import java.awt.Point;
import java.util.ArrayList;
import java.util.Collections;
import java.util.Comparator;
import java.util.List;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;
import java.util.concurrent.Future;
import util.*;

/**
 * <AUTHOR> REUVENI
 * Modified for Hare and Hounds game
 */
@SuppressWarnings("unused")
public class Model implements IModel, Constants
{
   private Board gameBoard;
   private int turnCount;
   private String difficulty = "Easy";
   private String AImsg = "";
   //private int leafs, recursion;
   private final Object counterLock = new Object();

   
   public Model()
   {
      setup();
   }

   // Controller reference not needed

   @Override
   public void setup()
   {
      // Initialize the board using the Board class
      gameBoard = new Board();

      // Reset turn count
      turnCount = 0;

      // Initialize Q-learning if not already done
      initializeQLearning();
   }

   /**
    * Initialize Q-learning system with training
    */
   private void initializeQLearning() {
      // Try to load existing Q-table
      try {
         AiModel.loadQTable("qlearning_hare_hounds.dat");
         Debug.logI("Loaded existing Q-table");
      } catch (Exception e) {
         Debug.logI("No existing Q-table found, starting fresh training");

         // Train the Q-learning agent with self-play
         Debug.logI("Starting Q-learning training...");
         AiModel.trainSelfPlay(1000); // Train with 1000 games

         // Save the trained Q-table
         AiModel.saveQTable("qlearning_hare_hounds.dat");
         Debug.logI("Q-learning training completed and saved");
      }
   }

   @Override
   public char[][] getBoard()
   {
      return gameBoard.getGrid();
   }

   public void setMove(Move move)
   {
      // Use the Board class to make the move
      gameBoard.makeMove(move);

      // Increment turn count if it's a hound move
      if (move.getPlayerSign() == HOUND_SIGN) {
         turnCount++;
      }

      if(PRINT_BOARD)
      gameBoard.printBoard();

      Debug.logI(move.toString()+" played successfully !");
   }

   @Override
   public Move getAiMove(char playerSign) {
      try {
         // Start timing the AI calculation
         Timer timer = new Timer();
         timer.start();

         // Reset counters for performance metrics
         AiModel.resetCounters();

         // Use Q-learning to select the best move
         Move selectedMove = AiModel.qLearningDecision(gameBoard, playerSign, false); // false = not training

         timer.stop();

         // Update AI message with Q-learning statistics
         AImsg = ("Q-Learning - " + AiModel.getTrainingStats() +
                 " | Decision time: " + timer.getMillisPassed() + "ms");

         Debug.logI("Q-Learning selected move: " + (selectedMove != null ? selectedMove.toString() : "null"));
         Debug.logI(AImsg);

         return selectedMove;

      } catch (Exception e) {
         Debug.logE("Error in Q-learning AI move selection: " + e.getMessage());
         e.printStackTrace();
      }
      return null;
   }

   /**
    * Get all valid moves for the hare
    * @return List of valid positions the hare can move to
    */
   public List<Move> getValidHareMoves() {
      return gameBoard.getValidMoves(gameBoard.getHarePosition(), HARE_SIGN);
   }

   /**
    * Get all valid moves for a specific hound
    * @param houndPos The position of the hound
    * @return List of valid positions the hound can move to
    */
   public List<Move> getValidHoundMoves(Point houndPos) {
      return gameBoard.getValidMoves(houndPos, HOUND_SIGN);
   }

   /**
    * Check if the hare has won (reached the left side of the board)
    * @return true if the hare has won, false otherwise
    */
   public boolean isHareWinner() {
      return gameBoard.isHareWinner();
   }

   /**
    * Check if the hounds have won (trapped the hare)
    * @return true if the hounds have won, false otherwise
    */
   public boolean isHoundsWinner() {
      return gameBoard.isHoundsWinner();
   }

   /**
    * Check if the hounds are stalling (not advancing for 10 turns)
    * @return true if the hounds are stalling, false otherwise
    */
   public boolean areHoundsStalling() {
      return gameBoard.areHoundsStalling();
   }

   /**
    * check if the game is over
    * @return true if the game is over, false otherwise
    */
   public boolean isGameOver() {
      return isHareWinner() || isHoundsWinner() || areHoundsStalling();
   }
   /**
    * Reset the turn count
    */
   public void resetTurnCount() {
      turnCount = 0;
   }

   /**
    * Sets the diffculty when changed
    */

   @Override
   public void setDifficulty(String difficulty) {
      this.difficulty = difficulty;

      // Adjust Q-learning parameters based on difficulty
      // Note: This would require making epsilon and other parameters non-final in AiModel
      // For now, we'll just log the difficulty change
      Debug.logI("Difficulty set to: " + difficulty);

      // Different difficulties could use different trained models or parameters
      switch (difficulty.toLowerCase()) {
         case "easy":
            // Could load a less trained model or use higher exploration
            break;
         case "medium":
            // Standard trained model
            break;
         case "hard":
            // Fully trained model with low exploration
            break;
      }
  }

  /**
   * Trigger additional Q-learning training
   */
  public void trainQLearning(int numGames) {
      Debug.logI("Starting additional Q-learning training with " + numGames + " games");
      AiModel.trainSelfPlay(numGames);
      AiModel.saveQTable("qlearning_hare_hounds.dat");
      Debug.logI("Additional training completed and saved");
  }

   /**
    * Check if a move is valid for a specific player
    * @param fromRow The row of the starting position
    * @param fromCol The column of the starting position
    * @param toRow The row of the destination position
    * @param toCol The column of the destination position
    * @param playerSign The player's sign (HARE_SIGN or HOUND_SIGN)
    * @return true if the move is valid, false otherwise
    */
   public boolean isValidMove(Move move) {

      return gameBoard.isValidMove(move);
   }



   public List<Board> getValidBoards(Board fboard, char playerSign){
      List<Move> possibleMoves = new ArrayList<>();
      possibleMoves = fboard.getValidMoves(playerSign);
      List<Board> possibleBoards = new ArrayList<>();
      for (Move move : possibleMoves) {
          Board board = new Board(fboard);
          board.makeMove(move);
          possibleBoards.add(board);
      }
      return possibleBoards;
  }

  /**
   * Determines the move that was made between two board states.
   * Compares board1 (before) with board2 (after) to identify what piece moved and where.
   * @param board1 The initial board state
   * @param board2 The resulting board state after a move
   * @return The Move object representing the change between the two boards
   */
  public Move boardToMove(Board board1, Board board2) {
      char[][] grid1 = board1.getGrid();
      char[][] grid2 = board2.getGrid();

      int fromRow = -1, fromCol = -1;
      int toRow = -1, toCol = -1;
      char sign = ' ';

      // Find where a piece was removed (from position)
      for (int i = 0; i < BOARD_ROWS; i++) {
          for (int j = 0; j < BOARD_COLS; j++) {
              if ((grid1[i][j] == HARE_SIGN || grid1[i][j] == HOUND_SIGN) && 
                  grid1[i][j] != grid2[i][j] && grid2[i][j] == EMPTY_SIGN) {
                  fromRow = i;
                  fromCol = j;
                  sign = grid1[i][j];
              }
          }
      }

      // Find where a piece was added (to position)
      for (int i = 0; i < BOARD_ROWS; i++) {
          for (int j = 0; j < BOARD_COLS; j++) {
              if (grid1[i][j] == EMPTY_SIGN && grid2[i][j] == sign) {
                  toRow = i;
                  toCol = j;
              }
          }
      }

      // Create and return the move
      if (fromRow != -1 && toRow != -1) {
          return new Move(fromRow, fromCol, toRow, toCol, sign);
      }

      return null; // Return null if no move was found
  }

  public Move nextMove(Board board){
      return boardToMove(gameBoard, board);
  }

  public String getAImsg(){
      return AImsg;
  }
   
}


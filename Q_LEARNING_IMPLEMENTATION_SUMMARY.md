# Q-Learning Implementation Summary

## Overview
Successfully replaced the minimax algorithm in the Hare and Hounds game with a Q-learning reinforcement learning implementation while maintaining full compatibility with the existing codebase interface.

## What Was Implemented

### 1. **Q-Learning Core Components** (in `AiModel.java`)

#### **State Representation**
- `encodeState(Board board)`: Creates unique string representation of game state
- Encodes hare position and sorted hound positions for consistency
- Format: "H{row},{col}|D{row},{col}|D{row},{col}|D{row},{col}"

#### **Action Representation**
- `encodeAction(Move move)`: Creates unique string representation of moves
- Format: "{fromRow},{fromCol}->{toRow},{toCol}:{playerSign}"

#### **Q-Table Management**
- HashMap-based Q-table: `Map<String, Map<String, Double>>`
- Thread-safe access with synchronized blocks
- `getQValue()` and `updateQValue()` methods for Q-table operations

#### **Learning Parameters**
- Learning Rate (α): 0.1
- Discount Factor (γ): 0.95
- Initial Epsilon: 0.3
- Minimum Epsilon: 0.01
- Epsilon Decay: 0.995

### 2. **Decision Making**

#### **Epsilon-Greedy Strategy**
- `selectAction()`: Implements exploration vs exploitation
- During training: Uses epsilon-greedy for exploration
- During gameplay: Uses best known Q-values (exploitation)

#### **Q-Learning Update Rule**
- `updateQLearning()`: Implements Q(s,a) = Q(s,a) + α[r + γ*max(Q(s',a')) - Q(s,a)]
- Updates Q-values based on immediate reward and future state value

### 3. **Reward Function**

#### **Terminal Rewards**
- Win: +1000/-1000 (depending on player perspective)
- Loss: -1000/+1000
- Stall: +500/-500 (hare benefits from hound stalling)

#### **Intermediate Rewards**
- **Hare**: Distance to goal, mobility, distance from hounds
- **Hounds**: Forward progress, limiting hare mobility, proximity to hare

### 4. **Training System**

#### **Self-Play Training**
- `trainSelfPlay(int numGames)`: Automated training through self-play
- Alternates between hare and hound moves
- Updates Q-values after each move
- Includes progress reporting every 100 games

#### **Persistence**
- `saveQTable()` and `loadQTable()`: Q-table persistence to file
- Automatic loading of existing Q-table on startup
- Fallback training if no existing Q-table found

### 5. **Interface Compatibility**

#### **Maintained Existing Interface**
- `getAiMove(char playerSign)` in Model.java still returns Move objects
- Replaced minimax calls with `qLearningDecision()`
- Updated performance metrics to show Q-learning statistics

#### **Statistics and Monitoring**
- Tracks total games, moves, exploration vs exploitation
- `getTrainingStats()`: Comprehensive statistics string
- Performance timing maintained for compatibility

## Key Changes Made

### **AiModel.java**
1. **Removed**: Minimax algorithm, alpha-beta pruning, evaluation functions
2. **Added**: Q-learning core, state/action encoding, reward calculation
3. **Replaced**: `minMax()` with `qLearningDecision()`
4. **Maintained**: `getLeafsCount()` and `getRecursionCount()` for compatibility

### **Model.java**
1. **Updated**: `getAiMove()` to use Q-learning instead of minimax
2. **Added**: Q-learning initialization in `setup()`
3. **Added**: Training methods and difficulty adjustment hooks
4. **Maintained**: Same return types and interface contracts

## Benefits of Q-Learning Implementation

### **Adaptive Learning**
- AI improves through experience rather than fixed evaluation
- Can discover novel strategies not programmed explicitly
- Adapts to different playing styles over time

### **Reduced Computational Complexity**
- No deep tree search required during gameplay
- O(1) decision making after training (table lookup)
- Faster response times compared to minimax

### **Flexibility**
- Easy to adjust learning parameters
- Can be retrained for different difficulty levels
- Supports online learning during gameplay

## Usage

### **Normal Gameplay**
- AI automatically uses trained Q-table for decisions
- No changes required to existing game interface
- Performance metrics updated to show Q-learning stats

### **Training**
- Automatic training on first run (1000 games)
- Manual training available via `trainQLearning(int numGames)`
- Q-table automatically saved and loaded

### **Difficulty Adjustment**
- Framework in place for difficulty-based parameter adjustment
- Can load different trained models for different difficulties
- Exploration rate can be adjusted per difficulty level

## Testing
- Created `QLearningTest.java` for verification
- Tests basic decision making, training functionality, and encoding
- Compilation successful, runtime testing confirms functionality

## Future Enhancements
1. **Deep Q-Learning**: Replace table with neural network
2. **Experience Replay**: Add replay buffer for better learning
3. **Multi-Agent Learning**: Separate agents for hare and hounds
4. **Online Learning**: Continue learning during gameplay
5. **Difficulty Tuning**: Fine-tune parameters per difficulty level

The implementation successfully maintains all existing interfaces while providing a modern, adaptive AI system that can learn and improve over time.

package model;

import java.awt.Point;
import java.util.ArrayList;
import java.util.List;

import controller.Constants;

/**
 * The Board class represents the game board for the Hare and Hounds game.
 * It manages the grid, positions of the hare and hounds, and game logic for moves and win conditions.
 * This class implements the Constants interface to access game configuration values.
 */
public class Board implements Constants {
    /**
     * 2D array representing the game board grid
     */
    private char[][] grid;

    /**
     * Current position of the hare on the board
     */
    private Point harePosition;

    /**
     * List of current positions for all hounds on the board
     */
    private List<Point> houndPositions;

    /**
     * Counter to track the number of turns taken by the hounds
     * Used to detect stalling
     */
    private int turnCount;

    /**
     * Default constructor that creates a new game board with initial positions
     * for the hare and hounds according to game rules
     */
    public Board() {
        grid = new char[BOARD_ROWS][BOARD_COLS];
        houndPositions = new ArrayList<>();
        initialize();
    }

    /**
     * Copy constructor for creating deep copies of a board
     * Used for game state analysis and AI move evaluation
     *
     * @param other The board to copy
     */
    public Board(Board other) {
        this.grid = new char[BOARD_ROWS][BOARD_COLS];
        for (int i = 0; i < BOARD_ROWS; i++) {
            for (int j = 0; j < BOARD_COLS; j++) {
                this.grid[i][j] = other.grid[i][j];
            }
        }

        this.harePosition = new Point(other.harePosition);
        this.houndPositions = new ArrayList<>();
        for (Point hound : other.houndPositions) {
            this.houndPositions.add(new Point(hound));
        }
    }

    /**
     * Initializes the game board to its starting state
     * - Sets all grid positions to empty
     * - Places the three hounds on the left side of the board
     * - Places the hare on the right side of the board
     * - Resets the turn counter
     */
    private void initialize() {
        // Clear the board
        for (int row = 0; row < BOARD_ROWS; row++) {
            for (int col = 0; col < BOARD_COLS; col++) {
                grid[row][col] = EMPTY_SIGN;
            }
        }

        // Initialize hounds (left side of the board)
        Point hound1Pos = POSITION_COORDINATES[TOP_ONE];
        Point hound2Pos = POSITION_COORDINATES[MIDDLE_ONE];
        Point hound3Pos = POSITION_COORDINATES[BOTTOM_ONE];

        placeHound(hound1Pos);
        placeHound(hound2Pos);
        placeHound(hound3Pos);

        // Initialize hare (right side)
        Point harePos = POSITION_COORDINATES[MIDDLE_FIVE];
        placeHare(harePos);

        turnCount = 0;
    }

    /**
     * Executes a move on the board for either the hare or a hound
     * Updates the grid and the corresponding piece position
     * Increments turn count when a hound moves
     *
     * @param fromRow Row position to move from
     * @param fromCol Column position to move from
     * @param toRow Row position to move to
     * @param toCol Column position to move to
     * @param playerSign Character representing the piece being moved (HARE_SIGN or HOUND_SIGN)
     */
    public void makeMove(int fromRow, int fromCol, int toRow, int toCol, char playerSign) {
        grid[fromRow][fromCol] = EMPTY_SIGN;
        grid[toRow][toCol] = playerSign;

        Point newPos = new Point(toRow, toCol);
        if (playerSign == HARE_SIGN) {
            harePosition = newPos;
        } else {
            // Update hound position
            for (int i = 0; i < houndPositions.size(); i++) {
                Point hound = houndPositions.get(i);
                if (hound.x == fromRow && hound.y == fromCol) {
                    houndPositions.set(i, newPos);
                    break;
                }
            }
            turnCount++;
        }
    }

    public void makeMove(Move move) {
        makeMove(move.getProw(), move.getPcol(), move.getRow(), move.getCol(), move.getPlayerSign());
    }

    /**
     * Places a hound at the specified position on the board
     * Updates both the grid and the hound positions list
     *
     * @param pos The position to place the hound
     */
    private void placeHound(Point pos) {
        grid[pos.x][pos.y] = HOUND_SIGN;
        houndPositions.add(new Point(pos));
    }

    /**
     * Places the hare at the specified position on the board
     * Updates both the grid and the hare position
     *
     * @param pos The position to place the hare
     */
    private void placeHare(Point pos) {
        grid[pos.x][pos.y] = HARE_SIGN;
        harePosition = new Point(pos);
    }

    /**
     * Gets all valid moves for a piece at the specified position
     * Checks all connected positions and filters by game rules
     *
     * @param position The current position of the piece
     * @param playerSign The type of piece (HARE_SIGN or HOUND_SIGN)
     * @return List of valid destination points the piece can move to
     */
    public List<Move> getValidMoves(Point position, char playerSign) {
        List<Move> validMoves = new ArrayList<>();
        List<Point> connections = VALID_CONNECTIONS.get(position);

        if (connections != null) {
            for (Point connection : connections) {
                Move move = new Move(position.x, position.y, connection.x, connection.y, playerSign);
                if (isValidMove(move)) {
                    validMoves.add(move);
                }
            }
        }
        return validMoves;
    }

    public List<Move> getValidMoves(char playerSign) {
        if(playerSign == HARE_SIGN)return getValidHareMoves();
         else return getValidHoundsMoves();
    }

    public List<Move> getValidHareMoves() {
        List<Move> validMoves = new ArrayList<>();

        List<Point> connections = VALID_CONNECTIONS.get(this.harePosition);

        if (connections != null) {
            for (Point connection : connections) {
                Move move = new Move(this.harePosition.x, this.harePosition.y, connection.x, connection.y, HARE_SIGN);
                if (isValidMove(move)) {
                    validMoves.add(move);
                }
            }
        }
        return validMoves;
    }

    public List<Move> getValidHoundsMoves() {
        List<Move> validMoves = new ArrayList<>();
        for (Point hound : this.houndPositions) {
            List<Point> connections = VALID_CONNECTIONS.get(hound);
            
        if (connections != null) {
            for (Point connection : connections) {
                Move move = new Move(hound.x, hound.y, connection.x, connection.y, HOUND_SIGN);
                if (isValidMove(move)) {
                    validMoves.add(move);
                }
            }
        }
            
        }
        

        return validMoves;
    }

    /**
     * Checks if a move is valid according to game rules:
     * - Must follow board connections
     * - Destination must be empty
     * - Hounds can only move forward (to the right)
     *
     * @param fromRow Starting row position
     * @param fromCol Starting column position
     * @param toRow Destination row position
     * @param toCol Destination column position
     * @param playerSign The type of piece being moved
     * @return true if the move is valid, false otherwise
     */
    public boolean isValidMove(Move move) {
        // Check if destination is empty
        if (grid[move.getRow()][move.getCol()] != EMPTY_SIGN) {
            return false;
        }

        if (MOVE_LEGALITY_CHECK_OFF) return true;


        // Check if the move follows board connections
        if (!Constants.isValidMove(move)) {
            return false;
        }

        
        // Hounds can only move forward (right)
        if (move.getPlayerSign() == HOUND_SIGN && move.getCol() < move.getPcol()) {
            return false;
        }

        return true;
    }

    /**
     * Checks if the hare has won the game
     * Hare wins if it reaches the left side of the board or gets behind all hounds
     *
     * @return true if the hare has won, false otherwise
     */
    public boolean isHareWinner() {
        // Hare wins if it reaches any of the positions on the left side of the board
        return harePosition.y == 0 ||
               (harePosition.y <= houndPositions.get(0).y &&
                harePosition.y <= houndPositions.get(1).y &&
                harePosition.y <= houndPositions.get(2).y); // Hare is behind all hounds
     }

    /**
     * Checks if the hounds have won the game
     * Hounds win if the hare has no valid moves (is trapped)
     *
     * @return true if the hounds have won, false otherwise
     */
    public boolean isHoundsWinner() {
        return getValidMoves(harePosition, HARE_SIGN).isEmpty();
    }

    /**
     * Checks if the hounds are stalling (taking too many turns)
     * Used to prevent indefinite games
     *
     * @return true if hounds have taken 10 or more turns, false otherwise
     */
    public boolean areHoundsStalling() {
        if (SATLLING_CHECK_OFF) return false;
        return turnCount >= 10;
    }


    /**
     * check if the game is over
     * @return true if the game is over, false otherwise
     */
    public boolean isGameOver() {
        return isHareWinner() || isHoundsWinner() || areHoundsStalling();
    }


    /**
     * Prints a text representation of the current board state to the console
     * Used for debugging and console-based gameplay
     */
    public void printBoard() {
        for (int i = 0; i < (BOARD_ROWS); i++) {
            for (int j = 0; j < BOARD_COLS; j++) {
                System.out.print("| " + this.grid[i][j] + " ");
            }
            System.out.println("|");
        }
        System.out.println();
    }

    // Getters
    /**
     * @return The 2D grid representing the board (direct reference)
     */
    public char[][] getGrid() { return grid; }

    /**
     * @return A copy of the hare's current position
     */
    public Point getHarePosition() { return new Point(harePosition); }

    /**
     * @return A copy of the list containing all hound positions
     */
    public List<Point> getHoundPositions() {
        return new ArrayList<>(houndPositions);
    }

    /**
     * Gets the piece character at the specified grid position
     *
     * @param row The row coordinate
     * @param col The column coordinate
     * @return The character at the specified position (EMPTY_SIGN, HARE_SIGN, or HOUND_SIGN)
     */
    public char getPiece(int row, int col) { return grid[row][col]; }


    public int getTurnCount(){
        return turnCount;
    }

    

    

}
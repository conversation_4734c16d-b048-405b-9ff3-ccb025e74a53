package view;

import controller.*;
import java.awt.BasicStroke;
import java.awt.BorderLayout;
import java.awt.Color;
import java.awt.Dimension;
import java.awt.FlowLayout;
import java.awt.Graphics;
import java.awt.Graphics2D;
import java.awt.Point;
import java.awt.RenderingHints;
import java.awt.geom.Ellipse2D;
import java.util.List;
import javax.swing.BorderFactory;
import javax.swing.ImageIcon;
import javax.swing.JButton;
import javax.swing.JComboBox;
import javax.swing.JFrame;
import javax.swing.JLabel;
import javax.swing.JMenu;
import javax.swing.JMenuBar;
import javax.swing.JMenuItem;
import javax.swing.JOptionPane;
import javax.swing.JPanel;
import javax.swing.SwingConstants;
import javax.swing.border.EmptyBorder;
import util.*;

/**
 * <AUTHOR> REUVENI
 * Modified for Hare and Hounds game
 */
public class View extends JFrame implements IView, Constants
{
   private Controller controller;
   private final JButton[][] matrixButtons;
   private final JLabel lblInfo;
   private final JLabel lblSecondaryInfo;  // New secondary info label
   private final BoardPanel boardPanel;
   private boolean soundB = SOUND;

  @SuppressWarnings("unused")
   public View()
   {
      // Set application icon
      try {
         ImageIcon appIcon = new ImageIcon(getClass().getResource("/assets/app_icon.png"));
         setIconImage(appIcon.getImage());
      } catch (Exception e) {
         Debug.logE("Failed to load application icon: " + e.getMessage());
      }

      // create Menu Bar
      // -------------------------------------------------------------
      JMenuBar menuBar = new JMenuBar();
      JMenu aboutMenu = new JMenu("About");
      JMenu settingsMenu = new JMenu(UNICODE_SETTINGS);
      JMenuItem game = new JMenuItem("Game Rules");
      game.addActionListener(e -> {
         JOptionPane.showMessageDialog(this, """
                                             Hare and Hounds Game Rules:
                                             
                                             1. The game is played on a board with 11 connected points.
                                             2. One player controls the Hare (H), the other controls three Hounds (D).
                                             3. The Hounds start first.
                                             4. The Hare can move in any direction along the connected lines.
                                             5. The Hounds can only move forward (right) along the connected lines.
                                             6. The Hare wins by reaching the left side of the board.
                                             7. The Hounds win by trapping the Hare so it cannot move.
                                             8. If the Hounds do not advance for 10 turns, the Hare wins.""",
            "Game Rules", JOptionPane.INFORMATION_MESSAGE);
      });
      JMenuItem author = new JMenuItem("Author");
      author.addActionListener(e -> {
         JOptionPane.showMessageDialog(this, "Created by DAVID REUVENI\nModified for Hare and Hounds game",
            "Author", JOptionPane.INFORMATION_MESSAGE);
      });
      JMenuItem exit = new JMenuItem(UNICODE_CLOSE);
      exit.addActionListener(e -> {
         int result = JOptionPane.showConfirmDialog(this, "Are you sure?", "Exit Game",
            JOptionPane.YES_NO_OPTION, JOptionPane.QUESTION_MESSAGE);
         if (result == JOptionPane.YES_OPTION) {
            System.exit(0);
         }
      });
      JMenuItem sound = new JMenuItem(UNICODE_SOUND);
      sound.addActionListener(e -> {
         if(soundB){
            Sound.stopAll();
            soundB=!soundB;
         }else{
            Sound.loop(SOUND_BACKGROUND);
            soundB=!soundB;
         }
      });
      
          
      aboutMenu.add(game);
      aboutMenu.add(author);
      settingsMenu.add(sound);
      settingsMenu.add(exit);  
      menuBar.add(aboutMenu);
      menuBar.add(settingsMenu);
      setJMenuBar(menuBar);

      // create action buttons (NORTH)
      // -------------------------------------------------------------
      JPanel pnlActions = new JPanel(new FlowLayout());
      pnlActions.setBorder(BorderFactory.createEmptyBorder(10, 3, 10, 3));
      Color backgroundColor = new Color(200, 225, 250); // Light blue background
      pnlActions.setBackground(backgroundColor); // Set the same background color

      JButton btnNewGame = new JButton("New Game");
      btnNewGame.setFocusable(false);
      btnNewGame.addActionListener(e -> {
         if(soundB)Sound.play(SOUND_TAP);
         Debug.logI("View NewGameButtonClicked !");
         controller.newGame();
      });

      JButton btnAiMove = new JButton("AI Move");
      btnAiMove.setFocusable(false);
      btnAiMove.addActionListener(e -> {
         Debug.logI("View AiMoveButtonClicked !");  
         if(soundB){
            Sound.play(SOUND_TAP);
            Sound.loop(SOUND_AI,1.0f,24000);}
         controller.aiMoveButtonClicked();
         Sound.stop(SOUND_AI);

      });
      String[] difficulties = { "Easy", "Medium", "Hard" };
      JComboBox<String> difficultyCombo = new JComboBox<>(difficulties);
      difficultyCombo.addActionListener(e -> {
         if(soundB)Sound.play(SOUND_TAP);
         Debug.logI("View DifficultyChanged to : " + difficultyCombo.getSelectedItem());
         String selected = (String) difficultyCombo.getSelectedItem();
         controller.onDifficultyChanged(selected);
     });

     JButton btnSideSwitch = new JButton("Switch Sides");
      btnSideSwitch.setFocusable(false);
      btnSideSwitch.addActionListener(e -> {
         if(soundB)Sound.play(SOUND_TAP);
         Debug.logI("View SideSwitchButtonClicked !");
         controller.debugSwitchTurn();
      });
     

      if (SIDE_SWITCHER)pnlActions.add(btnSideSwitch);
      pnlActions.add(btnNewGame);
      pnlActions.add(btnAiMove);
    //pnlActions.add(difficultyCombo);


      // create board panel (CENTER)
      // -------------------------------------------------------------
      boardPanel = new BoardPanel();
      boardPanel.setBackground(backgroundColor); // Set the same background color

      // create board buttons
      matrixButtons = new JButton[BOARD_ROWS][BOARD_COLS];
      for (int row = 0; row < BOARD_ROWS; row++)
      {
         for (int col = 0; col < BOARD_COLS; col++)
         {
            if (Constants.isValidPosition(row, col)) {
               JButton btn = new RoundButton();
               btn.setPreferredSize(new Dimension(BUTTON_SIZE, BUTTON_SIZE));
               btn.setBackground(Color.WHITE);
               btn.setFocusable(false);
               btn.setName(row + "," + col);
               btn.addActionListener(e -> {
                  Debug.logI("View boardButtonClicked at: " + btn.getName());
                  if(soundB)
                  Sound.play(SOUND_TAP);
                  String[] btnLocation = btn.getName().split(",");
                  int btnRow = Integer.parseInt(btnLocation[0]);
                  int btnCol = Integer.parseInt(btnLocation[1]);
                  controller.boardButtonClicked(btnRow, btnCol);
               });
               btn.setFont(FONT_BUTTONS);
               boardPanel.add(btn);
               matrixButtons[row][col] = btn;
            }
         }
      }

      // create Info Labels (SOUTH)
      // -------------------------------------------------------------
      JPanel infoPanel = new JPanel(new BorderLayout());  // Panel to hold both labels
      infoPanel.setBorder(BorderFactory.createEmptyBorder(3, 7, 5, 7));
      infoPanel.setBackground(backgroundColor); // Set the same background color

      lblInfo = new JLabel("Hare's Turn");
      lblInfo.setFont(FONT_BUTTONS);
      lblInfo.setBorder(BorderFactory.createEmptyBorder(3, 7, 5, 7));
      lblInfo.setOpaque(false); // Set to transparent

      lblSecondaryInfo = new JLabel("");  // Initialize with empty text
      lblSecondaryInfo.setFont(FONT_BUTTONS);
      lblSecondaryInfo.setBorder(BorderFactory.createEmptyBorder(3, 7, 5, 7));
      lblSecondaryInfo.setHorizontalAlignment(SwingConstants.RIGHT);  // Align to the right
      lblSecondaryInfo.setOpaque(false); // Set to transparent

      infoPanel.add(lblInfo, BorderLayout.WEST);  // Left side
      infoPanel.add(lblSecondaryInfo, BorderLayout.EAST);  // Right side

      add(pnlActions, BorderLayout.NORTH);
      add(boardPanel, BorderLayout.CENTER);
      add(infoPanel, BorderLayout.SOUTH);  // Add the panel instead of just lblInfo

      // setup window
      setTitle(WIN_TITLE);
      setDefaultCloseOperation(JFrame.EXIT_ON_CLOSE);
      setSize(1000, 695);  // Larger window size
      setLocationRelativeTo(null);
   }

   @Override
   public void setController(Controller controller)
   {
      this.controller = controller;
   }

   @Override
   public void setup(char[][] board, char playerTurn)
   {
      for (int row = 0; row < BOARD_ROWS; row++)
      {
         for (int col = 0; col < BOARD_COLS; col++)
         {
            if (Constants.isValidPosition(row, col)) {
               matrixButtons[row][col].setIcon(getSignIcon(board[row][col]));
               matrixButtons[row][col].setEnabled(true);
            }
         }
      }
      updatePlayerTurn(playerTurn);
      repaint();
      
      // Play sounds without blocking the UI thread
      if(soundB) {
         Sound.stopAll();
         Sound.play(SOUND_START);
         
         // Use a separate thread for the delay before playing background music
         new Thread(() -> {
            try {
               Thread.sleep(1500);
               if(soundB) { // Check again in case sound was turned off during sleep
                  Sound.loop(SOUND_BACKGROUND);
               }
            } catch (InterruptedException e) {
               Debug.logE("Background music start interrupted");
            }
         }).start();
      }
   }

   @Override
   public void updateMove(char[][] board, int row, int col, int prow, int pcol)
   {
      // Check if previous position is valid before trying to update it
      if (prow >= 0 && prow < BOARD_ROWS && pcol >= 0 && pcol < BOARD_COLS && 
          matrixButtons[prow][pcol] != null) {
         // remove previous sign
         matrixButtons[prow][pcol].setIcon(null);
      }

      // set new sign
      matrixButtons[row][col].setIcon(getSignIcon(board[row][col]));

      repaint();
   }

   private ImageIcon getSignIcon(char sign)
   {
      if (sign == EMPTY_SIGN)
         return null;

      if (sign == HARE_SIGN)
         return ICON_HARE;
      return ICON_HOUND;
   }

   @Override
   public void updatePlayerTurn(char playerTurn)
   {
      if (playerTurn == HARE_SIGN) {
         lblInfo.setText("Hare's Turn");
      } else {
         lblInfo.setText("Hounds' Turn");
      }
   }

   @Override
   public void updateInfo(String msg)
   {
      lblInfo.setText(msg);
   }

   @Override
   public void showMsg(String msg, String ttl){
      JOptionPane.showMessageDialog(null, msg, ttl, JOptionPane.INFORMATION_MESSAGE);     
   }

   /**
    * Custom panel to draw the Hare and Hounds board
    */
   private class BoardPanel extends JPanel {
      private static final long serialVersionUID = 1L;
      private Point[] pixelPositions;

      public BoardPanel() {
         setLayout(null);
         setBackground(new Color(200, 225, 250)); // Light blue background
      }

      @Override
      protected void paintComponent(Graphics g) {
         super.paintComponent(g);

         int width = getWidth();
         int height = getHeight();

         // Calculate position sizes
         int nodeSize = 90; // Size to match the button size

         // Calculate margins and spacing
         int margin = 50;
         int horizontalSpacing = (width - 2 * margin) / 4;
         int verticalSpacing = (height - 2 * margin) / 2;

         // Calculate positions for each node based on a 3x5 grid layout without corners
         pixelPositions = new Point[NUM_POSITIONS];

         // Create a 3x5 grid (without the 4 corners)
         calculatePositions(margin, horizontalSpacing, verticalSpacing);

         // Draw the connections between positions
         drawConnections(g);

         // Position the buttons
         positionButtons(nodeSize);
      }

      /**
       * Calculate the positions of all nodes on the board
       */
      private void calculatePositions(int margin, int horizontalSpacing, int verticalSpacing) {
         // First row (3 positions)
         pixelPositions[TOP_ONE] = new Point(margin + horizontalSpacing, margin);
         pixelPositions[TOP_TWO] = new Point(margin + 2 * horizontalSpacing, margin);
         pixelPositions[TOP_THREE] = new Point(margin + 3 * horizontalSpacing, margin);

         // Second row (all 5 positions)
         pixelPositions[MIDDLE_ONE] = new Point(margin, margin + verticalSpacing);
         pixelPositions[MIDDLE_TWO] = new Point(margin + horizontalSpacing, margin + verticalSpacing);
         pixelPositions[MIDDLE_THREE] = new Point(margin + 2 * horizontalSpacing, margin + verticalSpacing);
         pixelPositions[MIDDLE_FOUR] = new Point(margin + 3 * horizontalSpacing, margin + verticalSpacing);
         pixelPositions[MIDDLE_FIVE] = new Point(margin + 4 * horizontalSpacing, margin + verticalSpacing);

         // Third row (3 positions)
         pixelPositions[BOTTOM_ONE] = new Point(margin + horizontalSpacing, margin + 2 * verticalSpacing);
         pixelPositions[BOTTOM_TWO] = new Point(margin + 2 * horizontalSpacing, margin + 2 * verticalSpacing);
         pixelPositions[BOTTOM_THREE] = new Point(margin + 3 * horizontalSpacing, margin + 2 * verticalSpacing);
      }

      /**
       * Draw the connections between positions
       */
      private void drawConnections(Graphics g) {
         // Cast to Graphics2D for more advanced drawing options
         Graphics2D g2d = (Graphics2D) g;
         
         // Set a thicker stroke for the lines (adjust the value to change thickness)
         g2d.setStroke(new BasicStroke(6.0f));
         
         // Set the line color
         g2d.setColor(new Color(180, 180, 180));
         
         // Enable antialiasing for smoother lines
         g2d.setRenderingHint(RenderingHints.KEY_ANTIALIASING, RenderingHints.VALUE_ANTIALIAS_ON);

         // Draw connections between adjacent positions
         for (int i = 0; i < NUM_POSITIONS; i++) {
            for (int j = i + 1; j < NUM_POSITIONS; j++) {
               if (arePositionsConnected(i, j)) {
                  g2d.drawLine(pixelPositions[i].x, pixelPositions[i].y,
                            pixelPositions[j].x, pixelPositions[j].y);
               }
            }
         }
      }

      /**
       * Check if two positions are connected
       */
      private boolean arePositionsConnected(int i, int j) {
         Point pos1 = POSITION_COORDINATES[i];
         Point pos2 = POSITION_COORDINATES[j];

         List<Point> connections = VALID_CONNECTIONS.get(pos1);
         if (connections != null) {
            for (Point connectedPos : connections) {
               if (connectedPos.x == pos2.x && connectedPos.y == pos2.y) {
                  return true;
               }
            }
         }

         return false;
      }

      /**
       * Position the buttons on the board
       */
      private void positionButtons(int nodeSize) {
         for (int i = 0; i < NUM_POSITIONS; i++) {
            Point pixelPos = pixelPositions[i];
            int x = pixelPos.x - nodeSize / 2;
            int y = pixelPos.y - nodeSize / 2;

            Point boardPos = POSITION_COORDINATES[i];
            if (matrixButtons[boardPos.x][boardPos.y] != null) {
               // Position the button to exactly fit the 50x50 assets with a small margin
               matrixButtons[boardPos.x][boardPos.y].setBounds(x, y, nodeSize, nodeSize);
            }
         }
      }


   }

   /**
    * Updates the secondary info label with the given message
    * @param msg The message to display
    */
   @Override
   public void updateSecondaryInfo(String msg) {
      
      if (DEBUG_LOG_MODE)
      lblSecondaryInfo.setText(msg);
   }

   // Create a custom round button class
   private class RoundButton extends JButton {
      private static final long serialVersionUID = 1L;
      
      public RoundButton() {
         // Remove content area painting and use empty border
         setContentAreaFilled(false);
         setFocusPainted(false);
         setBorder(new EmptyBorder(0, 0, 0, 0));
      }
      
      @Override
      protected void paintComponent(Graphics g) {
         if (getModel().isArmed()) {
            g.setColor(Color.lightGray);
         } else {
            g.setColor(getBackground());
         }
         
         Graphics2D g2d = (Graphics2D) g;
         g2d.setRenderingHint(RenderingHints.KEY_ANTIALIASING, RenderingHints.VALUE_ANTIALIAS_ON);
         g2d.fillOval(0, 0, getSize().width - 1, getSize().height - 1);
         
         super.paintComponent(g);
      }
      
      @Override
      protected void paintBorder(Graphics g) {
         Graphics2D g2d = (Graphics2D) g;
         g2d.setRenderingHint(RenderingHints.KEY_ANTIALIASING, RenderingHints.VALUE_ANTIALIAS_ON);
         g2d.setColor(Color.DARK_GRAY);
         
         // Use a thinner stroke for the button borders (1.0f is the default thickness)
         g2d.setStroke(new BasicStroke(1.0f));
         
         g2d.drawOval(0, 0, getSize().width - 1, getSize().height - 1);
      }
      
      @Override
      public boolean contains(int x, int y) {
         // Only clickable within the oval shape
         return new Ellipse2D.Float(0, 0, getWidth(), getHeight()).contains(x, y);
      }
   }

   @Override
   public void playGameOverSound() {
      Sound.stopAll();
      if (Math.random() < 0.5) {
         Sound.play(SOUND_WINNER);
      } else {
         Sound.play(SOUND_LOSER);
      }
   }
}











package model;

/**
 * Represents a move in the Hare and Hounds game
 */
public class Move
{
    private int prow, row;
    private int pcol, col;
    private char sign;

    /**
     * Constructor for a move
     * @param prow The starting row
     * @param pcol The starting column
     * @param row The destination row
     * @param col The destination column
     * @param sign The player's sign (HARE_SIGN or HOUND_SIGN)
     */
    public Move(int prow, int pcol, int row, int col, char sign)
    {
        
        this.pcol = pcol;
        this.prow = prow;
        this.row = row;
        this.col = col;
        this.sign = sign;
    }

    public int getProw()
    {
        return prow;
    }

    public void setProw(int prow)
    {
        this.prow = prow;
    }

    public int getPcol()
    {
        return pcol;
    }

    public void setPcol(int pcol)
    {
        this.pcol = pcol;
    }

    public int getRow()
    {
        return row;
    }

    public void setRow(int row)
    {
        this.row = row;
    }

    public int getCol()
    {
        return col;
    }

    public void setCol(int col)
    {
        this.col = col;
    }

    public char getPlayerSign()
    {
        return sign;
    }

    public void setSign(char sign)
    {
        this.sign = sign;
    }

    @Override
    public String toString()
    {
        return "Move [from=(" + prow + "," + pcol + "), to=(" + row + "," + col + "), sign=" + sign + "]";
    }
}

package model;

/**
 * <AUTHOR> REUVENI
 * Modified for Hare and Hounds game
 */
public interface IModel
{
   public void setup();

   public char[][] getBoard();

   public void setMove(Move move);

   public Move getAiMove(char playerSign) throws InterruptedException;

   public void setDifficulty(String difficulty);

   public boolean isGameOver();

   public boolean isValidMove(Move move);

   public String getAImsg();
}

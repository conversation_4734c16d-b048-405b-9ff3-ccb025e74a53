package util;

import controller.Constants;

public class Debug {
    public static void logI(String message) {
      if (Constants.DEBUG_LOG_MODE && !Constants.DEBUG_JUST_ERRORS_WARNINGS) {
          System.out.println("[INF] "+message);
      }
  }

  public static void logW(String message) {
      if (Constants.DEBUG_LOG_MODE) {
          System.out.println("[WRN] "+message);
      }
  }


  public static void logE(String message) {
      if (Constants.DEBUG_LOG_MODE) {
          System.out.println("[ERR] "+message);
      }
  }


}


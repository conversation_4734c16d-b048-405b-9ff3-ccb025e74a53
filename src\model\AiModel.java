package model;

import controller.Constants;
import java.awt.Point;
import java.util.*;
import java.util.concurrent.ThreadLocalRandom;
import java.io.*;


/**
 * <AUTHOR> REUVENI
 * Modified for Hare and Hounds game - Q-Learning Implementation
 */
public class AiModel implements Constants{
    // Q-Learning parameters
    private static final double LEARNING_RATE = 0.1;
    private static final double DISCOUNT_FACTOR = 0.95;
    private static final double INITIAL_EPSILON = 0.3;
    private static final double MIN_EPSILON = 0.01;
    private static final double EPSILON_DECAY = 0.995;

    // Q-Table: State -> Action -> Q-Value
    private static final Map<String, Map<String, Double>> qTable = new HashMap<>();

    // Current exploration rate
    private static double epsilon = INITIAL_EPSILON;

    // Training statistics
    private static int totalGames = 0;
    private static int totalMoves = 0;
    private static int explorationMoves = 0;
    private static int exploitationMoves = 0;

    // Thread safety
    private static final Object qTableLock = new Object();

    public static int getLeafsCount() {
        return exploitationMoves;
    }

    public static int getRecursionCount() {
        return totalMoves;
    }

    /**
     * Generates a unique string representation of the board state for Q-table indexing
     */
    private static String encodeState(Board board) {
        StringBuilder sb = new StringBuilder();

        // Encode hare position
        Point harePos = board.getHarePosition();
        sb.append("H").append(harePos.x).append(",").append(harePos.y);

        // Encode hound positions (sorted for consistency)
        List<Point> houndPos = new ArrayList<>(board.getHoundPositions());
        houndPos.sort((p1, p2) -> {
            if (p1.x != p2.x) return Integer.compare(p1.x, p2.x);
            return Integer.compare(p1.y, p2.y);
        });

        for (Point hound : houndPos) {
            sb.append("|D").append(hound.x).append(",").append(hound.y);
        }

        return sb.toString();
    }

    /**
     * Generates a unique string representation of a move for Q-table indexing
     */
    private static String encodeAction(Move move) {
        return move.getProw() + "," + move.getPcol() + "->" +
               move.getRow() + "," + move.getCol() + ":" + move.getPlayerSign();
    }

public static List<Board> getValidBoards(Board fboard, char playerSign){
    List<Move> possibleMoves = fboard.getValidMoves(playerSign);
    List<Board> possibleBoards = new ArrayList<>();
    for (Move move : possibleMoves) {
        Board board = new Board(fboard);
        board.makeMove(move);
        possibleBoards.add(board);
    }
    return possibleBoards;
}

public static void resetCounters(){
    totalMoves = 0;
    explorationMoves = 0;
    exploitationMoves = 0;
}

    /**
     * Calculates the reward for a given board state and player
     * Used for Q-learning updates
     *
     * @param board The board state to evaluate
     * @param playerSign The player whose perspective to evaluate from
     * @return Reward value for the state
     */
    public static double calculateReward(Board board, char playerSign) {
        // Terminal rewards
        if (board.isHareWinner()) {
            return playerSign == HARE_SIGN ? 1000.0 : -1000.0;
        }
        if (board.isHoundsWinner()) {
            return playerSign == HARE_SIGN ? -1000.0 : 1000.0;
        }
        if (board.areHoundsStalling()) {
            return playerSign == HARE_SIGN ? 500.0 : -500.0;
        }

        // Intermediate rewards for strategic positioning
        double reward = 0.0;
        Point harePos = board.getHarePosition();
        List<Point> houndPos = board.getHoundPositions();

        if (playerSign == HARE_SIGN) {
            // Reward hare for moving towards goal (left side)
            reward += (BOARD_COLS - harePos.y) * 10.0;

            // Reward hare for having more mobility
            reward += board.getValidHareMoves().size() * 5.0;

            // Reward hare for being far from hounds
            double minDistance = Double.MAX_VALUE;
            for (Point hound : houndPos) {
                double distance = Math.abs(harePos.x - hound.x) + Math.abs(harePos.y - hound.y);
                minDistance = Math.min(minDistance, distance);
            }
            reward += minDistance * 2.0;

        } else { // HOUND_SIGN
            // Reward hounds for advancing (moving right)
            double avgHoundY = houndPos.stream().mapToInt(p -> p.y).average().orElse(0);
            reward += avgHoundY * 5.0;

            // Reward hounds for limiting hare mobility
            reward += (10 - board.getValidHareMoves().size()) * 8.0;

            // Reward hounds for being close to hare
            double avgDistanceToHare = houndPos.stream()
                .mapToDouble(h -> Math.abs(h.x - harePos.x) + Math.abs(h.y - harePos.y))
                .average().orElse(0);
            reward += (10 - avgDistanceToHare) * 3.0;
        }

        return reward;
    }

    /**
     * Gets the Q-value for a given state-action pair
     */
    private static double getQValue(String state, String action) {
        synchronized (qTableLock) {
            return qTable.computeIfAbsent(state, k -> new HashMap<>())
                         .getOrDefault(action, 0.0);
        }
    }

    /**
     * Updates the Q-value for a given state-action pair
     */
    private static void updateQValue(String state, String action, double qValue) {
        synchronized (qTableLock) {
            qTable.computeIfAbsent(state, k -> new HashMap<>())
                  .put(action, qValue);
        }
    }

    /**
     * Selects an action using epsilon-greedy strategy
     */
    private static Move selectAction(Board board, char playerSign, boolean isTraining) {
        List<Move> validMoves = board.getValidMoves(playerSign);
        if (validMoves.isEmpty()) {
            return null;
        }

        String state = encodeState(board);

        // During training, use epsilon-greedy
        if (isTraining && ThreadLocalRandom.current().nextDouble() < epsilon) {
            // Exploration: random action
            explorationMoves++;
            return validMoves.get(ThreadLocalRandom.current().nextInt(validMoves.size()));
        }

        // Exploitation: best known action
        exploitationMoves++;
        Move bestMove = null;
        double bestQValue = Double.NEGATIVE_INFINITY;

        for (Move move : validMoves) {
            String action = encodeAction(move);
            double qValue = getQValue(state, action);

            if (qValue > bestQValue) {
                bestQValue = qValue;
                bestMove = move;
            }
        }

        // If no Q-values exist yet, return random move
        if (bestMove == null) {
            return validMoves.get(ThreadLocalRandom.current().nextInt(validMoves.size()));
        }

        return bestMove;
    }


    /**
     * Q-Learning update method
     */
    public static void updateQLearning(String currentState, String action, double reward,
                                     String nextState, List<Move> nextValidMoves, char playerSign) {
        double currentQ = getQValue(currentState, action);

        // Find maximum Q-value for next state
        double maxNextQ = 0.0;
        if (nextValidMoves != null && !nextValidMoves.isEmpty()) {
            for (Move nextMove : nextValidMoves) {
                String nextAction = encodeAction(nextMove);
                double nextQ = getQValue(nextState, nextAction);
                maxNextQ = Math.max(maxNextQ, nextQ);
            }
        }

        // Q-learning update rule: Q(s,a) = Q(s,a) + α[r + γ*max(Q(s',a')) - Q(s,a)]
        double newQ = currentQ + LEARNING_RATE * (reward + DISCOUNT_FACTOR * maxNextQ - currentQ);
        updateQValue(currentState, action, newQ);
    }

    /**
     * Main Q-learning decision method - replaces minimax
     * This is the main entry point called by the Model class
     */
    public static Move qLearningDecision(Board board, char playerSign, boolean isTraining) {
        totalMoves++;

        Move selectedMove = selectAction(board, playerSign, isTraining);

        // Decay epsilon for training
        if (isTraining && epsilon > MIN_EPSILON) {
            epsilon *= EPSILON_DECAY;
        }

        return selectedMove;
    }

    /**
     * Training method for self-play learning
     */
    public static void trainSelfPlay(int numGames) {
        for (int game = 0; game < numGames; game++) {
            Board board = new Board();
            char currentPlayer = HARE_SIGN;

            while (!board.isGameOver()) {
                String currentState = encodeState(board);
                Move move = selectAction(board, currentPlayer, true);

                if (move == null) break;

                String action = encodeAction(move);
                board.makeMove(move);

                String nextState = encodeState(board);
                double reward = calculateReward(board, currentPlayer);
                List<Move> nextMoves = board.getValidMoves(currentPlayer == HARE_SIGN ? HOUND_SIGN : HARE_SIGN);

                updateQLearning(currentState, action, reward, nextState, nextMoves, currentPlayer);

                // Switch players
                currentPlayer = (currentPlayer == HARE_SIGN) ? HOUND_SIGN : HARE_SIGN;
            }

            totalGames++;
            if (game % 100 == 0) {
                System.out.println("Completed " + game + " training games. Epsilon: " + String.format("%.3f", epsilon));
            }
        }
    }

    /**
     * Save Q-table to file for persistence
     */
    public static void saveQTable(String filename) {
        try (ObjectOutputStream oos = new ObjectOutputStream(new FileOutputStream(filename))) {
            synchronized (qTableLock) {
                oos.writeObject(qTable);
            }
        } catch (IOException e) {
            System.err.println("Error saving Q-table: " + e.getMessage());
        }
    }

    /**
     * Load Q-table from file
     */
    @SuppressWarnings("unchecked")
    public static void loadQTable(String filename) {
        try (ObjectInputStream ois = new ObjectInputStream(new FileInputStream(filename))) {
            synchronized (qTableLock) {
                Map<String, Map<String, Double>> loadedTable =
                    (Map<String, Map<String, Double>>) ois.readObject();
                qTable.clear();
                qTable.putAll(loadedTable);
            }
        } catch (IOException | ClassNotFoundException e) {
            System.err.println("Error loading Q-table: " + e.getMessage());
        }
    }

    /**
     * Get training statistics
     */
    public static String getTrainingStats() {
        return String.format("Games: %d, Moves: %d, Exploration: %d, Exploitation: %d, Epsilon: %.3f, Q-States: %d",
            totalGames, totalMoves, explorationMoves, exploitationMoves, epsilon, qTable.size());
    }

    /**
     * Reset training statistics
     */
    public static void resetTrainingStats() {
        totalGames = 0;
        totalMoves = 0;
        explorationMoves = 0;
        exploitationMoves = 0;
        epsilon = INITIAL_EPSILON;
    }

    /**
     * Main entry point for AI move selection - maintains compatibility with existing interface
     * This replaces the old minimax approach
     */
    public static int evaluateBoard(Board board, char playerSign) {
        // For compatibility with existing ScoredBoard system, we'll return a score
        // based on Q-values of available moves
        List<Move> validMoves = board.getValidMoves(playerSign);
        if (validMoves.isEmpty()) {
            return playerSign == HARE_SIGN ? -10000 : 10000;
        }

        String state = encodeState(board);
        double bestQValue = Double.NEGATIVE_INFINITY;

        for (Move move : validMoves) {
            String action = encodeAction(move);
            double qValue = getQValue(state, action);
            bestQValue = Math.max(bestQValue, qValue);
        }

        // Convert Q-value to integer score for compatibility
        return (int) (bestQValue * 100);
    }
}

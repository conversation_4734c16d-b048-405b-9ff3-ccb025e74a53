package test;

import model.*;
import controller.Constants;

/**
 * Simple test class to verify Q-learning implementation
 */
public class QLearningTest implements Constants {
    
    public static void main(String[] args) {
        System.out.println("Testing Q-Learning Implementation for Hare and Hounds");
        
        // Test 1: Basic Q-learning decision making
        testBasicDecision();
        
        // Test 2: Training functionality
        testTraining();
        
        // Test 3: State and action encoding
        testEncoding();
        
        System.out.println("All tests completed!");
    }
    
    private static void testBasicDecision() {
        System.out.println("\n=== Test 1: Basic Decision Making ===");
        
        Board board = new Board();
        System.out.println("Created new board");
        
        // Test hare move selection
        Move hareMove = AiModel.qLearningDecision(board, HARE_SIGN, false);
        System.out.println("Hare move selected: " + (hareMove != null ? hareMove.toString() : "null"));
        
        // Test hound move selection
        Move houndMove = AiModel.qLearningDecision(board, HOUND_SIGN, false);
        System.out.println("Hound move selected: " + (houndMove != null ? houndMove.toString() : "null"));
        
        System.out.println("Basic decision test completed");
    }
    
    private static void testTraining() {
        System.out.println("\n=== Test 2: Training Functionality ===");
        
        System.out.println("Initial stats: " + AiModel.getTrainingStats());
        
        // Run a small training session
        System.out.println("Running 10 training games...");
        AiModel.trainSelfPlay(10);
        
        System.out.println("After training stats: " + AiModel.getTrainingStats());
        System.out.println("Training test completed");
    }
    
    private static void testEncoding() {
        System.out.println("\n=== Test 3: State and Action Encoding ===");
        
        Board board = new Board();
        
        // Test reward calculation
        double hareReward = AiModel.calculateReward(board, HARE_SIGN);
        double houndReward = AiModel.calculateReward(board, HOUND_SIGN);
        
        System.out.println("Initial hare reward: " + hareReward);
        System.out.println("Initial hound reward: " + houndReward);
        
        // Test board evaluation
        int hareEval = AiModel.evaluateBoard(board, HARE_SIGN);
        int houndEval = AiModel.evaluateBoard(board, HOUND_SIGN);
        
        System.out.println("Hare board evaluation: " + hareEval);
        System.out.println("Hound board evaluation: " + houndEval);
        
        System.out.println("Encoding test completed");
    }
}
